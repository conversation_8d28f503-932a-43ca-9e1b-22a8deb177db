# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
test_*.py
*_test.py
*.test.ts
*.test.js
*.spec.ts
*.spec.js
/tests/
__tests__/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# crush local state
.crush/

# Cloudflare Workers & OpenNext Build Artifacts
.wrangler/
.open-next/

# CRITICAL: OpenNext build artifacts contain sensitive environment variables
# These directories are auto-generated and contain API keys/secrets
.open-next/**/*.env
.open-next/**/next-env.mjs
.open-next/server-functions/
.open-next/cloudflare/

# Python virtual environments and cache
__pycache__/
*.py[cod]
*$py.class
venv/
env/
.venv/
.env/
python/

# CraCha Ingestion Pipeline (Modal.com Service)
# This is a separate Python service that doesn't need to be in the frontend repo
src/ingestion/
!src/ingestion/README.md

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/
