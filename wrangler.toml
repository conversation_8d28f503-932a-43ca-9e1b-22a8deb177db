name = "cracha-frontend"
main = ".open-next/worker/index.mjs"
compatibility_date = "2024-08-10"
workers_dev = true

# Static Assets Configuration
[assets]
directory = ".open-next/static"
binding = "ASSETS"

# Environment Variables
[vars]
# Application Config
NEXT_PUBLIC_APP_ENV = "development"
NEXT_PUBLIC_USE_REAL_API = "true"
NEXT_PUBLIC_APP_URL = "http://localhost:8787"

# Supabase Authentication
NEXT_PUBLIC_SUPABASE_URL = "https://ncfrgsqfnccjfyezxjsj.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Q3OaTFVoPtcC1VLYI1hZAJrDtXLNaMnfjCPx9bvogmk"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.jn9_u3HcwVnFqYVM_pPzSmrQvEWU14jU213xUs1p3VA"

# Worker URLs
NEXT_PUBLIC_CRACHA_WORKER_URL = "https://cracha-worker-rag.aimpact-agency.workers.dev"
NEXT_PUBLIC_ADMIN_WORKER_URL = "https://cracha-admin-worker.aimpact-agency.workers.dev"

# AI Provider Keys
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
GEMINI_API_KEY = "AIzaSyDhBaHG4dbHrHb-7MRC_-6aLk7_AA6rzWw"
VERTEX_KEY = "AIzaSyDhBaHG4dbHrHb-7MRC_-6aLk7_AA6rzWw"
GOOGLE_API_KEY = "AIzaSyDhBaHG4dbHrHb-7MRC_-6aLk7_AA6rzWw"

# Cloudflare Configuration (UPDATED TOKENS)
CLOUDFLARE_API_TOKEN = "****************************************"
CLOUDFLARE_API_KEY = "****************************************"
CLOUDFLARE_EMAIL = "<EMAIL>"
CLOUDFLARE_ACCOUNT_ID = "8c010bb7d3f4ebde9f695e61441511cb"
CLOUDFLARE_KV_NAMESPACE_ID = "417ae907fb8547758b969c5eeaa635dd"
DATABASE_REGISTRY_KV_ID = "417ae907fb8547758b969c5eeaa635dd"
VECTORIZE_API_TOKEN = "****************************************"
VECTORIZE_ACCOUNT_ID = "8c010bb7d3f4ebde9f695e61441511cb"
GLOBAL_API_KEY = "29bd2f55dbea6d4937d4f234dbc7bee582d4b"

# Development Environment
ENVIRONMENT = "development"
LOG_LEVEL = "debug"

# KV Bindings for database registry
[[kv_namespaces]]
binding = "DATABASE_REGISTRY"
id = "417ae907fb8547758b969c5eeaa635dd"

# Observability (enable logs for debugging)
[observability.logs]
enabled = true

# Production Environment
[env.production]
# KV Bindings for production
[[env.production.kv_namespaces]]
binding = "DATABASE_REGISTRY"
id = "417ae907fb8547758b969c5eeaa635dd"
[env.production.vars]
# Application Config
NEXT_PUBLIC_APP_ENV = "production"
NEXT_PUBLIC_USE_REAL_API = "true"
NEXT_PUBLIC_APP_URL = "https://cracha.aimpact-agency.workers.dev"

# Supabase Authentication - MUST be set in Cloudflare Dashboard
# Supabase Authentication - Production URLs
NEXT_PUBLIC_SUPABASE_URL = "https://ncfrgsqfnccjfyezxjsj.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Q3OaTFVoPtcC1VLYI1hZAJrDtXLNaMnfjCPx9bvogmk"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.jn9_u3HcwVnFqYVM_pPzSmrQvEWU14jU213xUs1p3VA"

# Worker URLs
NEXT_PUBLIC_CRACHA_WORKER_URL = "https://cracha-worker-rag.aimpact-agency.workers.dev"
NEXT_PUBLIC_ADMIN_WORKER_URL = "https://cracha-admin-worker.aimpact-agency.workers.dev"

# Cloudflare Configuration - CRITICAL: MUST be set in Cloudflare Dashboard
# These variables are REQUIRED for production and must be manually added:
# CLOUDFLARE_API_TOKEN = "****************************************"
# CLOUDFLARE_ACCOUNT_ID = "8c010bb7d3f4ebde9f695e61441511cb"
# CLOUDFLARE_KV_NAMESPACE_ID = "417ae907fb8547758b969c5eeaa635dd"
# DATABASE_REGISTRY_KV_ID = "417ae907fb8547758b969c5eeaa635dd"
# VECTORIZE_API_TOKEN = "****************************************"
# VECTORIZE_ACCOUNT_ID = "8c010bb7d3f4ebde9f695e61441511cb"

# AI Provider Keys - MUST be set in Cloudflare Dashboard
# OPENAI_API_KEY = "..."
# GEMINI_API_KEY = "..."
# VERTEX_KEY = "..."
# GOOGLE_API_KEY = "..."

# Production Environment
ENVIRONMENT = "production"
LOG_LEVEL = "info"

