@echo off
echo 🔐 Adding required secrets to cracha-frontend worker...

REM Cloudflare Configuration
type nul | set /p="8c010bb7d3f4ebde9f695e61441511cb" | wrangler secret put CLOUDFLARE_ACCOUNT_ID
type nul | set /p="****************************************" | wrangler secret put CLOUDFLARE_API_KEY
type nul | set /p="29bd2f55dbea6d4937d4f234dbc7bee582d4b" | wrangler secret put CLOUDFLARE_API_TOKEN
type nul | set /p="<EMAIL>" | wrangler secret put CLOUDFLARE_EMAIL
type nul | set /p="417ae907fb8547758b969c5eeaa635dd" | wrangler secret put CLOUDFLARE_KV_NAMESPACE_ID
type nul | set /p="417ae907fb8547758b969c5eeaa635dd" | wrangler secret put DATABASE_REGISTRY_KV_ID
type nul | set /p="29bd2f55dbea6d4937d4f234dbc7bee582d4b" | wrangler secret put GLOBAL_API_KEY

REM AI Provider Keys
type nul | set /p="AIzaSyDhBaHG4dbHrHb-7MRC_-6aLk7_AA6rzWw" | wrangler secret put GEMINI_API_KEY
type nul | set /p="********************************************************************************************************************************************************************" | wrangler secret put OPENAI_API_KEY
type nul | set /p="AIzaSyDhBaHG4dbHrHb-7MRC_-6aLk7_AA6rzWw" | wrangler secret put VERTEX_KEY

REM Vectorize Configuration
type nul | set /p="8c010bb7d3f4ebde9f695e61441511cb" | wrangler secret put VECTORIZE_ACCOUNT_ID
type nul | set /p="29bd2f55dbea6d4937d4f234dbc7bee582d4b" | wrangler secret put VECTORIZE_API_TOKEN

REM Supabase Authentication
type nul | set /p="https://ncfrgsqfnccjfyezxjsj.supabase.co" | wrangler secret put NEXT_PUBLIC_SUPABASE_URL
type nul | set /p="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Q3OaTFVoPtcC1VLYI1hZAJrDtXLNaMnfjCPx9bvogmk" | wrangler secret put NEXT_PUBLIC_SUPABASE_ANON_KEY
type nul | set /p="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.jn9_u3HcwVnFqYVM_pPzSmrQvEWU14jU213xUs1p3VA" | wrangler secret put SUPABASE_SERVICE_ROLE_KEY

echo ✅ All required secrets added successfully!
echo 🔍 Check with: wrangler secret list
echo 🚀 Your cracha-frontend worker is now fully configured!
pause