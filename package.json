{"name": "cracha-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:cf": "next build && opennextjs-cloudflare build", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "deploy:secrets": "node -e \"console.log('Use deploy-secrets.bat on Windows or deploy-secrets.sh on Linux/Mac')\"", "deploy:full": "npm run deploy:secrets && npm run deploy", "cf:dev": "wrangler dev", "type-check": "tsc --noEmit", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"@ai-sdk/gateway": "^1.0.7", "@ai-sdk/react": "^2.0.15", "@cloudflare/next-on-pages": "^1.13.14", "@hookform/resolvers": "^5.2.1", "@icons-pack/react-simple-icons": "^13.7.0", "@paper-design/shaders-react": "^0.0.46", "@prisma/client": "^6.14.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@supabase/auth-ui-react": "^0.4.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "^5.84.2", "ai": "^5.0.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "flatpickr": "^4.6.13", "gsap": "^3.13.0", "harden-react-markdown": "^1.0.4", "katex": "^0.16.22", "lucide-react": "^0.539.0", "motion": "^12.23.12", "next": "15.4.6", "next-themes": "^0.4.6", "ogl": "^1.0.11", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.5", "react-syntax-highlighter": "^15.6.1", "react-use-measure": "^2.1.7", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "server-only": "^0.0.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76", "zustand": "^5.0.7"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250810.0", "@eslint/eslintrc": "^3", "@opennextjs/cloudflare": "^1.6.5", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/flatpickr": "^3.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "url-loader": "^4.1.1", "wrangler": "^4.33.0"}}